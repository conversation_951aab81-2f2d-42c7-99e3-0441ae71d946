use std::io::Write;

pub fn save_log(
    command: &str,
    host_name: &str,
    dest_file: &str,
    msg: &str,
    echo: bool,
) -> std::io::Result<()> {
    let now = chrono::Local::now();
    let ymdhms = now.format("%Y-%m-%d %H:%M:%S").to_string();

    if echo {
        println!("{} {} {} {}", host_name, command, ymdhms, msg);
    }

    let mut file = std::fs::OpenOptions::new()
        .append(true)
        .create(true)
        .open(dest_file)?;
    
    writeln!(file, "{} {}", ymdhms, msg)?;
    Ok(())
}
